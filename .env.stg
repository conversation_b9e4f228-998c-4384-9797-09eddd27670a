APP_NAME=Laravel
APP_ENV=local
APP_KEY=base64:a7pJCej3cD5r4vtpMIxUww2AMHzhSmNWkTQysPuo/8Y=
APP_DEBUG=true
APP_URL=http://localhost:3002


FRONTEND_URL=https://front.action.jo
ASSET_URL=https://stg.action.jo
SESSION_DOMAIN=https://stg.action.jo
SESSION_SECURE_COOKIE=https://stg.action.jo

LOG_CHANNEL=stack
LOG_DEPRECATIONS_CHANNEL=null
LOG_LEVEL=debug


APP_PORT=3003
OCTANE_PORT=3002






DB_CONNECTION=mysql
DB_HOST=mysql
DB_DATABASE=backend
DB_USERNAME=sail
DB_PASSWORD=password
FORWARD_DB_PORT=3307



DB_HOST_OLD=action.cluster-cmxhe8k2shs7.eu-west-1.rds.amazonaws.com
DB_PORT_OLD=3306
DB_CONNECTION_OLD=mysql
DB_DATABASE_OLD=production
DB_USERNAME_OLD=root
DB_PASSWORD_OLD=superSecuerpasport




FILESYSTEM_DISK=s3
QUEUE_CONNECTION=sync
SESSION_DRIVER=redis
SESSION_LIFETIME=120
MEDIA_DISK="s3"
BROADCAST_DRIVER=log
CACHE_DRIVER=redis








MEMCACHED_HOST=memcached
REDIS_HOST=redis
REDIS_PASSWORD=null
REDIS_PORT=6379
FORWARD_REDIS_PORT=6378
MAIL_MAILER=smtp
MAIL_HOST=mailhog
MAIL_PORT=1025
MAIL_USERNAME=null
MAIL_PASSWORD=null
MAIL_ENCRYPTION=null
MAIL_FROM_ADDRESS="<EMAIL>"
MAIL_FROM_NAME="${APP_NAME}"

AWS_ACCESS_KEY_ID=********************
AWS_SECRET_ACCESS_KEY=eszSa4Qe6elcdB4yowEDTTb70N2M115vARfOwf2T
AWS_DEFAULT_REGION=eu-west-1
AWS_BUCKET=media.action.jo
AWS_USE_PATH_STYLE_ENDPOINT=false
AWS_URL=https://s3.eu-west-1.amazonaws.com/media.action.jo/

PUSHER_APP_ID=
PUSHER_APP_KEY=
PUSHER_APP_SECRET=
PUSHER_APP_CLUSTER=mt1
MIX_PUSHER_APP_KEY="${PUSHER_APP_KEY}"
MIX_PUSHER_APP_CLUSTER="${PUSHER_APP_CLUSTER}"

SCOUT_DRIVER=meilisearch
MEILISEARCH_HOST=http://meilisearch:7700
MEILISEARCH_KEY=variances
OCTANE_SERVER=swoole
OCTANE_HTTPS=false


SAIL_XDEBUG_MODE=develop,debug
Allow_File_Extension=jpeg,png,jpg,mp4,webm,webp,mov
OPENAI_ORGANIZATION=
OPENAI_API_KEY=***************************************************

ARAB_BANK_ACCESS_TOKEN="dc677240a86b3eff8fa16a0b2a7856f9"
ARAB_BANK_PROFILE_ID="B8F03506-E346-4289-A732-A9A4E380F328"
ARAB_BANK_SECRETKEY="1daf2b7d902b4aff8374e721646d944e39df29a6ab4f4ea5ab44c96f2e9cdf8f01837a460d0347dda247fc4de7f2842ee2376b6cb4f342aa8af0ef56322173ba31480115a5504759a275c17c0f6aa39ce299390733d64839992de73e6486001edb68e69dc53a474b978ea2b054275799feed8a32bca94f35bc47f60bea9da085"
ARAB_BANK_URL="https://testsecureacceptance.cybersource.com/pay"



HYPERPAY_SECRET="C9B6E0E9B8ABDEDC77AFFF14CA3E74D79C0414A0B7107A5D9BC8B8101C71278B"
HYPERPAY_ACCESS_TOKEN="OGFjN2E0Yzk3OTc3ZDY5ZTAxNzk3ODVkMzI5NjAwNDh8clc5dDloaG5Bag"
HYPERPAY_ENTITY_ID="8ac7a4c97977d69e0179785daa47004c"
HYPERPAY_URL="https://oppwa.com/"

HYPERPAY_DEVELOPMENT=TRUE
assetUrlTelescope=https://action.jo
CHOKIDAR_USEPOLLING=true
AWS_ACCESS_KEY_ID="********************"
AWS_SECRET_ACCESS_KEY="5LLRimPzWlXnuHX68ItJVrPh3ZyWHvFi2/ZrPwLU"




PHP_OPCACHE_ENABLE=1
DOCKER_PHP_VERSION="8.1"
SCOUT_QUEUE=false

WWWGROUP=1000
WWWUSER=1000




DB_CONNECTION_PODUCTION=mysql
DB_HOST_PODUCTION=v2.cluster-cmxhe8k2shs7.eu-west-1.rds.amazonaws.com
DB_PORT_PODUCTION=3306
DB_DATABASE_PODUCTION=backend
DB_USERNAME_PODUCTION=admin
DB_PASSWORD_PODUCTION=87a9sfHJAISf87asfihu7