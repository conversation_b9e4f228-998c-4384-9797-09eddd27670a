{"name": "laravel/laravel", "type": "project", "description": "The Laravel Framework.", "keywords": ["framework", "laravel"], "license": "MIT", "require": {"php": "^8.0.2", "ajcastro/eager-load-pivot-relations": "^0.3.0", "aws/aws-sdk-php": "^3.337", "doctrine/dbal": "^3.0", "giggsey/libphonenumber-for-php": "^8.13", "guzzlehttp/guzzle": "^7.2", "http-interop/http-factory-guzzle": "^1.2", "joggapp/laravel-google-translate": "^9.0", "khaled.alshamaa/ar-php": "*", "kutia-software-company/larafirebase": "^1.3", "laravel/fortify": "^1.16", "laravel/framework": "^9.2", "laravel/octane": "^1.2", "laravel/passport": "^11.6", "laravel/sanctum": "^2.14.1", "laravel/scout": "^10.2", "laravel/telescope": "^4.8", "laravel/tinker": "^2.7", "league/flysystem-aws-s3-v3": "^3.0", "maatwebsite/excel": "^3.1", "mbezhanov/faker-provider-collection": "^2.0", "meilisearch/meilisearch-php": "^1.7", "millat/laravel-hooks": "*", "openai-php/laravel": "^0.4.3", "orangehill/iseed": "^3.0", "phpoffice/phpspreadsheet": "^1.29", "psr/simple-cache": "2.0", "sentry/sentry-laravel": "^4.5", "spatie/laravel-activitylog": "^4.7", "spatie/laravel-medialibrary": "^10.0.0", "spatie/laravel-permission": "^5.11", "spatie/laravel-sitemap": "^6.4", "spatie/laravel-translatable": "^6.0", "staudenmeir/eloquent-has-many-deep": "^1.7", "stichoza/google-translate-php": "^5.1"}, "require-dev": {"fakerphp/faker": "^1.9.1", "laravel/sail": "*", "mockery/mockery": "^1.4.4", "nunomaduro/collision": "^6.1", "phpunit/phpunit": "^9.5.10", "reliese/laravel": "^1.1", "spatie/laravel-ignition": "^1.0"}, "autoload": {"psr-4": {"App\\": "app/", "Database\\Factories\\": "database/factories/", "Database\\Seeders\\": "database/seeders/", "Database\\Variants\\": "database/variants/"}, "files": ["app/Helper/helpers.php", "app/Helper/constants.php", "app/Helper/Ddh.php"]}, "autoload-dev": {"psr-4": {"Tests\\": "tests/"}}, "scripts": {"post-autoload-dump": ["Illuminate\\Foundation\\ComposerScripts::postAutoloadDump", "@php artisan package:discover --ansi"], "post-update-cmd": ["@php artisan vendor:publish --tag=laravel-assets --ansi --force"], "post-root-package-install": ["@php -r \"file_exists('.env') || copy('.env.example', '.env');\""], "post-create-project-cmd": ["@php artisan key:generate --ansi"]}, "extra": {"laravel": {"dont-discover": ["laravel/telescope"]}}, "config": {"optimize-autoloader": true, "preferred-install": "dist", "sort-packages": true, "allow-plugins": {"php-http/discovery": true}}, "minimum-stability": "dev", "prefer-stable": true}