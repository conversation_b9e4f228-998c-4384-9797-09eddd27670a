<?php

use App\Exceptions\GeneralException;
use App\Helper\CacheHelper;
use App\Helpers\FileHelper;

use App\Helpers\TranslationHelper;
use App\Models\Visitor;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Storage;
use Symfony\Component\HttpFoundation\Response;

/**
 * Get model class by name .
 */
if (!function_exists('get_model')) {
    /**
     * Resolve the given model class name string and return the corresponding model instance.
     *
     * @template T
     * @param class-string<T> $modelClass The class name of the model to resolve.
     * @return T An instance of the class specified by $modelClass.
     */
    function get_model($modelClass)
    {
        return app($modelClass);
    }
}

/**
 * Get current locale .
 */
if (!function_exists('current_locale')) {
    function current_locale(): string
    {
        return app()->getLocale();
    }
}

/**
 * Strip tags with trim .
 */
if (!function_exists('strip_tags_trim')) {
    function strip_tags_trim(string $string): string
    {
        $allowedTags = [
            '<p>',
            '<b>',
            '<a>',
            '<ul>',
            '<span>',
            '<div>',
            '<ol>',
            '<li>',
            '<i>',
            '<img>',
            '<strong>',
            '<h1>',
            '<h2>',
            '<h3>',
            '<h4>',
            '<h5>',
            '<h6>',
            '<small>',
            '<br>',
            '<hr>',
            '<table>',
            '<tr>',
            '<th>',
            '<td>',
            '<thead>',
            '<tbody>',
        ];

        return trim(preg_replace('/\s\s+/', ' ', strip_tags($string, $allowedTags)));
    }
}

/**
 * Determine if app is locale environment .
 */
if (!function_exists('is_local_env')) {
    function is_local_env(): bool
    {
        return !in_array(config('app.env'), ['prod', 'production']);
    }
}

/**
 * Get media url from storage .
 */
if (!function_exists('get_media_url')) {
    function get_media_url(string $path): string|null
    {
        $fileName = str($path)->afterLast('/')->replace('/', '')->toString();

        if (blank($fileName)) {
            return null;
        }

        return resolve(FileHelper::class)->getUrl($path);
    }
}

/**
 * Get Ip Address .
 */
if (!function_exists('ip_address')) {
    function ip_address(): mixed
    {
        $ip_address = request()->ip();

        if (in_array($ip_address, ['127.0.0.1', '::1'])) {
            $geoLocation = config('services.geo_location.default_data');
            if (array_key_exists('ip', $geoLocation)) {
                $ip_address = $geoLocation['ip'];
            }
        }

        return $ip_address;
    }
}


if (!function_exists('user_agent')) {
    function user_agent(): mixed
    {
        return request()->header('User-Agent') ?? 'Unknown';
    }
}



/**
 * Get request host name .
 */
if (!function_exists('http_host_name')) {
    function http_host_name($host = null): string
    {
        if (!$host) {
            $host = request()->getHost();
        }

        $http = config('app.http_status') ? 'https' : 'http';

        return $http . '://' . $host;
    }
}



/**
 * Upload file .
 */
if (!function_exists('file_upload')) {
    /**
     * @param string $pathPrefix
     * @param UploadedFile $file
     * @param null $disk
     * @return string|null
     */
    function file_upload(string $pathPrefix, UploadedFile $file, $disk = null): ?string
    {
        /**
         * @param $file
         * @param $pathPrefix
         * @return string
         * when app run on local env
         */
        $callLocalStorage = function ($file, $pathPrefix) {
            $file_name = $file->hashName();
            $pathPrefix = trim($pathPrefix, '/') . '/';
            $file->move(public_path($pathPrefix), $file_name);
            return $file_name;
        };

        /**
         * @param $file
         * @param $pathPrefix
         * @return string|null
         * when app run on production env
         */
        $callProductionStorage = function ($file, $pathPrefix) {
            $name = $file->hashName();
            $filePath = trim($pathPrefix, '/') . '/' . $name;

            Storage::put($filePath, file_get_contents($file));

            return $name;
        };

        if ($disk = 'local') {
            return $callLocalStorage($file, $pathPrefix);
        }

        if (config('filesystems.default') == 's3') {
            return $callProductionStorage($file, $pathPrefix);
        }

        return $callLocalStorage($file, $pathPrefix);
    }
}

/**
 * Get the server ip address .
 */
if (!function_exists('server_ip_address')) {
    function server_ip_address()
    {
        return request()->server->get('SERVER_ADDR') ?? null;
    }
}

/**
 * Check if the given class has the given trait  .
 */
if (!function_exists('has_trait')) {
    function has_trait(string|object $class, string|array $trait): bool
    {
        if (is_array($trait)) {
            return !array_diff($trait, class_uses($class));
        }

        return array_key_exists($trait, class_uses($class));
    }
}

/**
 * Check if the given class has the given interface  .
 */
if (!function_exists('abort_if_not_has_trait')) {
    /**
     * @throws GeneralException
     */
    function abort_if_not_has_trait(
        string|object $class,
        string|array $trait,
        int $code = Response::HTTP_INTERNAL_SERVER_ERROR
    ): void {
        if (!has_trait($class, $trait)) {
            throw new GeneralException(
                message: __(
                    'response-messages.class_not_have_trait',
                    [
                        'class' => (is_object($class) ? $class::class : $class),
                        'trait' => $trait
                    ]
                ),
                code: $code
            );
        }
    }
}

/**
 * Check if the given class implement the given interface  .
 */
if (!function_exists('has_interface')) {
    function has_interface(string|object $class, string|array $interface): bool
    {
        if (is_array($interface)) {
            return !array_diff($interface, class_implements($class));
        }

        return array_key_exists($interface, class_implements($class));
    }
}

/**
 * Translate the given message from the translation database.
 */
if (!function_exists('translate')) {
    function translate(string $key, array $replace = [], string $locale = null): string
    {
        return resolve(TranslationHelper::class)->get($key, $replace, $locale);
    }
}

if (!function_exists('get_repo_interface')) {
    function get_repo_interface(string $resource)
    {
        $resource = ucwords(str($resource)->singular()->camel()->toString());

        $interface = $resource . "RepositoryInterface";

        $path = "App\Repositories\\$resource\\$interface";

        return resolve($path);
    }
}



if (!function_exists('controller_name_without_suffix')) {
    function controller_name_without_suffix(string $namespace)
    {
        // Split the namespace by "\" to get individual parts
        $parts = explode('\\', $namespace);

        // Get the last part, which is the controller name
        $controllerNameWithSuffix = end($parts);

        // Remove the "Controller" suffix
        $controllerNameWithoutSuffix = strtolower(str_replace('Controller', '', $controllerNameWithSuffix));
        return $controllerNameWithoutSuffix;
    }
}


if (!function_exists('is_admin_route_from_uri')) {
    function is_admin_route_from_uri(string $route)
    {
        return !strpos($route, '/admin/') === false ? true : false;
    }

}

if (!function_exists('is_admin_route')) {
    function is_admin_route(string $namespace)
    {
        $parts = explode('\\', $namespace);
        return in_array("Admin", $parts);
    }

}


if (!function_exists('is_auth_route')) {
    function is_auth_route(string $route)
    {
        return !strpos($route, '/auth/') === false ? true : false;
    }
}





if (!function_exists('generate_route_name')) {
    function generate_route_name($route)
    {

        $path = $route->uri();
        // Remove any special characters and replace slashes with dots
        $name = str_replace(['/', '{', '}', '-', '_', 'api', 'v1', '..'], ['.', '', '', '', ''], $path);

        return $name;
    }
}




if (!function_exists('filter_routes_app')) {
    function filter_routes_app($routes)
    {


        return collect($routes)
            ->reject(function ($route) {
                $actionName = $route->getActionName();
                $methodSegments = explode('@', $actionName);
                $head = head($methodSegments);
                $uri = $route->uri();
                return strpos($uri, 'telescope') || (strpos($head, 'App\Http\Controllers') === false && strpos($head, '\App\Http\Controllers') === false);
            })->map(function ($route) {

                $actionName = $route->getActionName();
                $methodSegments = explode('@', $actionName);
                $methodName = last($methodSegments);
                $namespace = head($methodSegments);
                $uri = $route->uri();



                $name = $route->getName() ?? generate_route_name($route);

                $prefix = strpos($name, '/admin/') === false && is_admin_route($namespace) ? "admin." : "";

                $routeName = "$prefix$name";

                return [
                    'method' => implode('|', $route->methods()),
                    'uri' => $uri,
                    'name' => $name,
                    'action' => $actionName,
                    'methodName' => $methodName,
                    'routeName' => $routeName
                ];
            })->toArray();
        ;
    }
}




if (!function_exists('generateVisitorId')) {
    function generateVisitorId($query)
    {
        $date = date('Ymd');
        $randomNumber = rand(1000, 99999999999);
        $visitorId = $date . $randomNumber;

        return $visitorId;
    }
}


if (!function_exists('getSqlWithBindings')) {
    function getSqlWithBindings($query)
    {
        $sqlWithBindings = $query->toSql();
        $bindings = $query->getBindings();

        return vsprintf(str_replace('?', "'%s'", $sqlWithBindings), $bindings);
    }
}



if (!function_exists('isVisitor')) {
    function isVisitor()
    {
        $user = auth(GUARD_API)->user();
        $modelName = $user->getMorphClass();
        if ($modelName === Visitor::class) {
            return true;
        }
        return false;

    }
}



if (!function_exists('urlTitle')) {
    function urlTitle(string $str, string $separator = '-', bool $lowercase = false): string
    {
        $qSeparator = preg_quote($separator, '#');

        $trans = [
            '&.+?;' => '',
            '[^\w\d\pL\pM _-]' => '',
            '\s+' => $separator,
            '(' . $qSeparator . ')+' => $separator,
        ];

        $str = strip_tags($str);

        foreach ($trans as $key => $val) {
            $str = preg_replace('#' . $key . '#iu', $val, $str);
        }

        if ($lowercase === true) {
            $str = mb_strtolower($str);
        }

        return trim(trim($str, $separator));
    }
}



if (!function_exists('getCurrency')) {
    function getCurrency()
    {
        if (request()->hasHeader('currency')) {
            return request()->header('currency');
        } else {
            return 'JOD';
        }

    }
}



if (!function_exists('resizeImage')) {

    function resizeImage($src, $width, $quality = null)
    {
        $params = ["width=$width"];
        if ($quality) {
            $params[] = "quality=$quality";
        }
        $paramsString = join(",", $params);
        return "https://media.action.jo/cdn-cgi/image/$paramsString/$src";
    }
}



if (!function_exists('containsArabic')) {
    function containsArabic($string)
    {
        return preg_match('/[\p{Arabic}]/u', $string) === 1;
    }
}



if (!function_exists('getUserCached')) {
    /*************  ✨ Codeium Command ⭐  *************/
    /**
     * Get the authenticated user using the Bearer token
     *
     * @return \App\Models\User|false
     */
    /******  cac22faa-4322-43ad-a8aa-e50ff6c02422  *******/
    function getUserCached()
    {
        $token = request()->bearerToken();

        if (!$token) {
            return false;
        }

        $cacheKey = 'auth_' . GUARD_API . '_user_' . md5($token);

        $cacheHelper = new CacheHelper;

        return $cacheHelper->remember(
            key: $cacheKey,
            callback: function () use ($cacheKey) {
                return auth(GUARD_API)->user();

            },
            tags: [],
            ttl: 10000,
        );
    }
}

if (!function_exists('isAuthenticatedCached')) {
    function isAuthenticatedCached()
    {
        $token = request()->bearerToken();
        if (!$token) {
            return false;
        }

        $cacheKey = 'auth_' . GUARD_API . '_check_' . md5($token);

        return Cache::store('redis')->remember($cacheKey, now()->addMinutes(30), function () {
            return auth(GUARD_API)->check();
        });
    }
}