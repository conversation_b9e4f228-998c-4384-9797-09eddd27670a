<?php

namespace App\Providers;

use App\Models\Label;
use App\Repositories\ActivityLog\ActivityLogRepository;
use App\Repositories\ActivityLog\ActivityLogRepositoryInterface;
use App\Repositories\Ai\AiRepository;
use App\Repositories\Banner\BannerRepository;
use App\Repositories\Banner\BannerRepositoryInterface;
use App\Repositories\FCMToken\FCMTokenRepository;
use App\Repositories\FCMToken\FCMTokenRepositoryInterface;
use App\Repositories\SearchVariance\SearchVarianceRepository;
use App\Repositories\Slider\SliderRepository;
use App\Repositories\Slider\SliderRepositoryInterface;
use App\Repositories\Wallet\WalletRepository;
use App\Repositories\Wallet\WalletRepositoryInterface;
use Illuminate\Support\ServiceProvider;
use App\Repositories\Auth\AuthRepository;
use App\Repositories\Cart\CartRepository;
use App\Repositories\City\CityRepository;
use App\Repositories\Page\PageRepository;
use App\Repositories\Role\RoleRepository;
use App\Repositories\User\UserRepository;
use App\Repositories\Brand\BrandRepository;
use App\Repositories\Order\OrderRepository;
use App\Repositories\Price\PriceRepository;
use App\Repositories\Branch\BranchRepository;
use App\Repositories\Bundle\BundleRepository;
use App\Repositories\Rating\RatingRepository;
use App\Repositories\Search\SearchRepository;
use App\Repositories\Ai\AiRepositoryInterface;
use App\Repositories\Account\AccountRepository;
use App\Repositories\Address\AddressRepository;
use App\Repositories\Auction\AuctionRepository;
use App\Repositories\Country\CountryRepository;
use App\Repositories\Filters\FiltersRepository;
use App\Repositories\Invoice\InvoiceRepository;
use App\Repositories\Product\ProductRepository;
use App\Repositories\Visitor\VisitorRepository;
use App\Repositories\Category\CategoryRepository;
use App\Repositories\Currency\CurrencyRepository;
use App\Repositories\Language\LanguageRepository;
use App\Repositories\Supplier\SupplierRepository;
use App\Repositories\Variance\VarianceRepository;
use App\Repositories\Wishlist\WishlistRepository;
use App\Repositories\Auth\AuthRepositoryInterface;
use App\Repositories\Cart\CartRepositoryInterface;
use App\Repositories\City\CityRepositoryInterface;
use App\Repositories\Page\PageRepositoryInterface;
use App\Repositories\Role\RoleRepositoryInterface;
use App\Repositories\User\UserRepositoryInterface;
use App\Repositories\Attribute\AttributeRepository;
use App\Repositories\ContactUs\ContactUsRepository;
use App\Repositories\Promotion\PromotionRepository;
use App\Repositories\Brand\BrandRepositoryInterface;
use App\Repositories\Order\OrderRepositoryInterface;
use App\Repositories\Price\PriceRepositoryInterface;
use App\Repositories\Permission\PermissionRepository;
use App\Repositories\Branch\BranchRepositoryInterface;
use App\Repositories\Bundle\BundleRepositoryInterface;
use App\Repositories\Rating\RatingRepositoryInterface;
use App\Repositories\Search\SearchRepositoryInterface;
use App\Repositories\FilterGroup\FilterGroupRepository;
use App\Repositories\Transaction\TransactionRepository;
use App\Repositories\UsedProduct\UsedProductRepository;
use App\Repositories\Account\AccountRepositoryInterface;
use App\Repositories\Address\AddressRepositoryInterface;
use App\Repositories\Auction\AuctionRepositoryInterface;
use App\Repositories\Country\CountryRepositoryInterface;
use App\Repositories\Filters\FiltersRepositoryInterface;
use App\Repositories\Invoice\InvoiceRepositoryInterface;
use App\Repositories\Product\ProductRepositoryInterface;
use App\Repositories\Visitor\VisitorRepositoryInterface;
use App\Repositories\Notification\NotificationRepository;
use App\Repositories\ProductGroup\ProductGroupRepository;
use App\Repositories\UsedCategory\UsedCategoryRepository;
use App\Repositories\Category\CategoryRepositoryInterface;
use App\Repositories\Currency\CurrencyRepositoryInterface;
use App\Repositories\Language\LanguageRepositoryInterface;
use App\Repositories\Shipping\ShippingRepositoryInterface;
use App\Repositories\Supplier\SupplierRepositoryInterface;
use App\Repositories\Variance\VarianceRepositoryInterface;
use App\Repositories\Wishlist\WishlistRepositoryInterface;
use App\Repositories\PaymentMethod\PaymentMethodRepository;
use App\Repositories\ShippingOrder\ShippingOrderRepository;
use App\Repositories\Attribute\AttributeRepositoryInterface;
use App\Repositories\ContactUs\ContactUsRepositoryInterface;
use App\Repositories\Promotion\PromotionRepositoryInterface;
use App\Repositories\TranslationKey\TranslationKeyRepository;
use App\Repositories\TranslationTag\TranslationTagRepository;
use App\Repositories\Permission\PermissionRepositoryInterface;
use App\Repositories\ShippingCarrier\ShippingCarrierRepository;
use App\Repositories\FilterGroup\FilterGroupRepositoryInterface;
use App\Repositories\Transaction\TransactionRepositoryInterface;
use App\Repositories\UsedProduct\UsedProductRepositoryInterface;
use App\Repositories\TranslationValue\TranslationValueRepository;
use App\Repositories\Notification\NotificationRepositoryInterface;
use App\Repositories\ProductGroup\ProductGroupRepositoryInterface;
use App\Repositories\UsedCategory\UsedCategoryRepositoryInterface;
use App\Repositories\PaymentMethod\PaymentMethodRepositoryInterface;
use App\Repositories\ShippingOrder\ShippingOrderRepositoryInterface;
use App\Repositories\TranslationKey\TranslationKeyRepositoryInterface;
use App\Repositories\TranslationTag\TranslationTagRepositoryInterface;
use App\Repositories\CategoryFilterGroup\CategoryFilterGroupRepository;
use App\Repositories\ShippingCarrier\ShippingCarrierRepositoryInterface;
use App\Repositories\TranslationValue\TranslationValueRepositoryInterface;
use App\Repositories\CategoryFilterGroup\CategoryFilterGroupRepositoryInterface;
use App\Repositories\Label\LabelRepository;
use App\Repositories\Label\LabelRepositoryInterface;
use App\Repositories\RequestChange\RequestChangeRepositoryInterface;
use App\Repositories\RequestChange\RequestChangeRepository;
use App\Repositories\SearchVariance\SearchVarianceRepositoryInterface;

class RepositoryServiceProvider extends ServiceProvider
{
    /**
     * Register services.
     *
     * @return void
     */
    public function register()
    {
        /* Bind interfaces to repositories   */
        foreach ($this->getRepositoriesWithInterfaces() as $interface => $repository) {
            $this->app->bind($interface, $repository);
        }
        // $this->getRepositoriesWithInterfaces();
    }

    /**
     * Bootstrap services.
     *
     * @return void
     */
    public function boot()
    {
    }

    private function getRepositoriesWithInterfaces()
    {
        // $repositories = collect(File::glob(app_path('Repositories/*/*')));
        //
        // $repositories = $repositories->filter(fn($repo) => Str::contains($repo, 'Repository.php'));
        //
        // foreach ($repositories as $repository) {
        //     $repositoryPath = str_replace(
        //         '.php',
        //         '',
        //         str_replace('/', '\\', substr($repository, strpos($repository, 'Repositories/')))
        //     );
        //
        //     $repositoryClass = app("App\\$repositoryPath");
        //
        //     $interface = Str::replaceFirst('App\\' , '' , head(class_implements($repositoryClass)));
        //
        //     $this->app->bind($interface, $repositoryClass::class);
        // }

        return [
            TranslationKeyRepositoryInterface::class => TranslationKeyRepository::class,
            TranslationValueRepositoryInterface::class => TranslationValueRepository::class,
            TranslationTagRepositoryInterface::class => TranslationTagRepository::class,
            LanguageRepositoryInterface::class => LanguageRepository::class,
            BrandRepositoryInterface::class => BrandRepository::class,
            CategoryRepositoryInterface::class => CategoryRepository::class,
            VarianceRepositoryInterface::class => VarianceRepository::class,
            ProductRepositoryInterface::class => ProductRepository::class,
            AttributeRepositoryInterface::class => AttributeRepository::class,
            BundleRepositoryInterface::class => BundleRepository::class,
            ProductGroupRepositoryInterface::class => ProductGroupRepository::class,
            CountryRepositoryInterface::class => CountryRepository::class,
            CityRepositoryInterface::class => CityRepository::class,
            AddressRepositoryInterface::class => AddressRepository::class,
            SupplierRepositoryInterface::class => SupplierRepository::class,
            ShippingCarrierRepositoryInterface::class => ShippingCarrierRepository::class,
            AuctionRepositoryInterface::class => AuctionRepository::class,
            RatingRepositoryInterface::class => RatingRepository::class,
            PaymentMethodRepositoryInterface::class => PaymentMethodRepository::class,
            PriceRepositoryInterface::class => PriceRepository::class,
            CurrencyRepositoryInterface::class => CurrencyRepository::class,
            CartRepositoryInterface::class => CartRepository::class,
            AuthRepositoryInterface::class => AuthRepository::class,
            WishlistRepositoryInterface::class => WishlistRepository::class,
            AccountRepositoryInterface::class => AccountRepository::class,
            UserRepositoryInterface::class => UserRepository::class,
            CategoryFilterGroupRepositoryInterface::class => CategoryFilterGroupRepository::class,
            FiltersRepositoryInterface::class => FiltersRepository::class,
            FilterGroupRepositoryInterface::class => FilterGroupRepository::class,
            PageRepositoryInterface::class => PageRepository::class,
            OrderRepositoryInterface::class => OrderRepository::class,
            BranchRepositoryInterface::class => BranchRepository::class,
            UsedCategoryRepositoryInterface::class => UsedCategoryRepository::class,
            ContactUsRepositoryInterface::class => ContactUsRepository::class,
            UsedProductRepositoryInterface::class => UsedProductRepository::class,
            SearchRepositoryInterface::class => SearchRepository::class,
            \App\Repositories\SearchProduct\SearchRepositoryInterface::class => \App\Repositories\SearchProduct\SearchRepository::class,
            PromotionRepositoryInterface::class => PromotionRepository::class,
            TransactionRepositoryInterface::class => TransactionRepository::class,
            AiRepositoryInterface::class => AiRepository::class,
            PermissionRepositoryInterface::class => PermissionRepository::class,
            RoleRepositoryInterface::class => RoleRepository::class,
            NotificationRepositoryInterface::class => NotificationRepository::class,
            InvoiceRepositoryInterface::class => InvoiceRepository::class,
            ShippingOrderRepositoryInterface::class => ShippingOrderRepository::class,
            VisitorRepositoryInterface::class => VisitorRepository::class,
            ActivityLogRepositoryInterface::class => ActivityLogRepository::class,
            FCMTokenRepositoryInterface::class => FCMTokenRepository::class,
            RequestChangeRepositoryInterface::class => RequestChangeRepository::class,
            SliderRepositoryInterface::class => SliderRepository::class,
            WalletRepositoryInterface::class => WalletRepository::class,
            SearchVarianceRepositoryInterface::class => SearchVarianceRepository::class,
            BannerRepositoryInterface::class => BannerRepository::class,
            LabelRepositoryInterface::class => LabelRepository::class
        ];

    }
}