<?php
// app/Observers/ProductObserver.php
namespace App\Observers;

use App\Models\Cart;
use App\Models\Product;
use App\Models\Wishlist;
use PhpOffice\PhpSpreadsheet\Calculation\Statistical\Distributions\F;

class ProductObserver
{
    // public function updated(Product $product)
    // {

    //     // $product->load([
    //     //     'variances',
    //     //     'variances.stocks',
    //     //     'variances.activeStock',
    //     //     'stocks',
    //     //     'activeStock'
    //     // ]);
    //     // dd('updated', $product->variances, $product->activeStock);
    //     // dump('updated', $product->variances, $product->activeStock);
    //     // Product::setterMaxAndMinPrice($product);
    // }



    // public function created(Product $product)
    // {
    //     // dump('created');
    //     // $product->load([  
    //     //     'variances',
    //     //     'variances.stocks',
    //     //     'variances.activeStock',
    //     //     'stocks',
    //     //     'activeStock'
    //     // ]);

    //     // Product::setterMaxAndMinPrice($product);
    // }
    public function deleted(Product $product)
    {
        // unde
        $product->variances->each->delete();
        Cart::where('productId', $product->productId)->delete();
        Wishlist::where('productId', $product->productId)->delete();
        $product->shouldBeSearchable();

    }


    // public function saved(Product $product)
    // {
    //     // $product->load([
    //     //     'variances',
    //     //     'variances.stocks',
    //     //     'variances.activeStock',
    //     //     'stocks',
    //     //     'activeStock'
    //     // ]);
    //     // dump('saved', $product->variances[0]->activeStock);

    //     // Product::setterMaxAndMinPrice($product);

    // }


    // public function touched(Product $product)
    // {
    //     // parent::touch();
    //     // // $product->load([
    //     // //     'variances',
    //     // //     'variances.stocks',
    //     // //     'variances.activeStock',
    //     // //     'stocks',
    //     // //     'activeStock'
    //     // // ]);

    //     // // Product::setterMaxAndMinPrice($product);
    //     // dump('touched');
    // }
}