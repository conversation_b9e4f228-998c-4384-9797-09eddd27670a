<?php

namespace App\Traits;

use Exception;
use Illuminate\Support\Facades\App;
use Spatie\Translatable\HasTranslations as BaseHasTranslations;

trait HasTranslations
{
    use BaseHasTranslations;

    public function toArray()
    {


        $attributes = parent::toArray();
        //! TODO: GET to be changed to request->input (hasan) Done
        if (app()->runningInConsole() || (request()->has('trans') && request()->get('trans') == "off")) {
            return $attributes;
        }
        foreach ($this->getTranslatableAttributes() as $field) {
            $attributes[$field] = isset($attributes[$field][App::getLocale()]) ? $attributes[$field][App::getLocale()] : '';
        }

        return $attributes;
    }
}