<?php

namespace App\Http\Middleware;

use App\Models\Visitor;
use Closure;
use App\Traits\ApiResponser;
use Illuminate\Http\Request;

class AuthenticateOrVisitor
{

    use ApiResponser;

    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure(\Illuminate\Http\Request): (\Illuminate\Http\Response|\Illuminate\Http\RedirectResponse)  $next
     * @return \Illuminate\Http\Response|\Illuminate\Http\RedirectResponse
     */
    public function handle(Request $request, Closure $next)
    {


        $isAuthenticated = auth(GUARD_API)->check() || (request()->hasHeader('visitor-id') && request()->header('visitor-id') !== '');
        if (!$isAuthenticated) {
            return $this->sendError('Unauthorized');
        }

        if (request()->hasHeader('visitor-id') && request()->header('visitor-id') !== '' && !request()->hasHeader('Authorization')) {
            Visitor::makeAuthClient();
        }

        return $next($request);

    }
}