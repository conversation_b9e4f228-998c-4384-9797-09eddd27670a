<?php

namespace App\Http\Middleware;

use App\Traits\ApiResponser;
use Closure;
use Auth;
use Illuminate\Http\Request;
use Spatie\Activitylog\Facades\CauserResolver;

class Authenticate
{
    use ApiResponser;
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure(\Illuminate\Http\Request): (\Illuminate\Http\Response|\Illuminate\Http\RedirectResponse)  $next
     * @return \Illuminate\Http\Response|\Illuminate\Http\RedirectResponse
     */
    public function handle(Request $request, Closure $next)
    {
        $isAuthenticated = (auth(GUARD_API)->check());
        if (!$isAuthenticated) {
            return $this->sendError('Unauthorized');
        }

        $user = auth(GUARD_API)->user();

        CauserResolver::setCauser($user);

        return $next($request);
    }
}
