<?php

namespace App\Http\Middleware;

use App\Traits\ApiResponser;
use Closure;
use Auth;
use Illuminate\Http\Request;

class CheckAuth
{
    use ApiResponser;
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure(\Illuminate\Http\Request): (\Illuminate\Http\Response|\Illuminate\Http\RedirectResponse)  $next
     * @return \Illuminate\Http\Response|\Illuminate\Http\RedirectResponse
     */
    public function handle(Request $request, Closure $next)
    {
        $isAuthenticated = (Auth::check());

        if (!$isAuthenticated) {
            return $this->sendError('Unauthorized');
        }

        return $next($request);
    }
}