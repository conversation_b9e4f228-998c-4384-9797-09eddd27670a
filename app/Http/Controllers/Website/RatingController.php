<?php

namespace App\Http\Controllers\Website;

use App\Http\Resources\Admin\RatingResource;
use App\Repositories\Rating\RatingRepositoryInterface;
use Illuminate\Http\JsonResponse;
use App\Http\Controllers\ApiBaseController;


class RatingController extends ApiBaseController
{
   
    /**
     * @param  RatingRepositoryInterface $ratingRepository
     */
    public function __construct(protected RatingRepositoryInterface $ratingRepository)
    {
    }
    /**
     * Get User Ratings .
     * @return JsonResponse
     */
    public function index(): JsonResponse
    {
        $data = $this->ratingRepository->getUserRatings();
        return $this->sendSuccess(RatingResource::generate($data));
    }

}
