<?php

namespace App\Http\Controllers\Website;

use App\Http\Requests\Website\FCMTokenRequest;
use App\Http\Resources\Website\FcmTokenResource;
use App\Repositories\FCMToken\FCMTokenRepositoryInterface;
use Illuminate\Http\JsonResponse;
use App\Http\Controllers\ApiBaseController;


class FCMTokenController extends ApiBaseController
{

    /**
     * @param  FCMTokenRepositoryInterface $fcmTokenRepository
     */
    public function __construct(protected FCMTokenRepositoryInterface $fcmTokenRepository)
    {
    }
    /**
     * Get User Ratings .
     * @return JsonResponse
     */
    public function index(): JsonResponse
    {
        $data = $this->fcmTokenRepository->get();
        return $this->sendSuccess(FcmTokenResource::generate($data));
    }


    /**
     * Store a new Country record .
     * @param FCMTokenRequest $request
     * @return JsonResponse
     */
    public function store(FCMTokenRequest $request)
    {
        $data = $this->fcmTokenRepository->store($request->all());
        return $this->sendSuccess(FCMTokenResource::generate($data));
    }

    /**
     * Update Page record .
     *
     * @param FCMTokenRequest $request
     * @param int|string $id
     * @return JsonResponse
     */
    public function update(FCMTokenRequest $request, int|string $id): JsonResponse
    {
        $data = $this->fcmTokenRepository->updateToken($id, $request->all());
        return $this->sendSuccess(FCMTokenResource::generate($data));
    }


    /**
     * Show Page by slug .
     *
     * @param int|string $id
     * @return JsonResponse
     */
    public function show(int|string $id): JsonResponse
    {

        $data = $this->fcmTokenRepository->findTokenById($id);
        return $this->sendSuccess(FcmTokenResource::generate($data));
    }



    /**
     * Delete Address record .
     * @param int|string $id
     * @return JsonResponse
     */
    public function destroy(int|string $id)
    {
        $this->fcmTokenRepository->delete($id);
        return $this->sendSuccess();
    }


}
