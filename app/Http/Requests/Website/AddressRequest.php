<?php

namespace App\Http\Requests\Website;

use App\Rules\PhoneNumberValidator;
use App\Http\Requests\BaseFormRequest;

class AddressRequest extends BaseFormRequest
{

    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules()
    {
        return [
            'cityId' => 'required|integer|exists:cities,cityId',
            'district' => 'required|string',
            'phone' => ['required', new PhoneNumberValidator],
            'street' => 'nullable|string',
            'recipientName' => 'required|string',
            'apartmentNumber' => 'nullable|string',
            'buildingNumber' => 'nullable|string',
            'default' => 'nullable',

        ];

    }


}