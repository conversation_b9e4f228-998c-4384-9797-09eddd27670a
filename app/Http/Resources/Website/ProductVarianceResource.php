<?php

namespace App\Http\Resources\Website;

use App\Http\Resources\ApiJsonResource;
use App\Http\Resources\General\MediaResource;

class ProductVarianceResource extends ApiJsonResource
{
    /**
     * @param $request
     * @return array
     */
    public function toArray($request)
    {

        // dd($this->resource->stocks->first()->price);
        $response = [
            "varianceId" => $this->varianceId,
            "auctionId" => $this->auctionId,
            "name" => $this->name,
            "slug" => $this->slug,
            "brandId" => $this->brandId,
            "SKU" => $this->SKU,
            "type" => $this->type,
            "stock" => StockResource::generate($this->whenLoaded('activeStock')),
            "attributes" => AttributeVarianceResource::generate($this->whenLoaded('attributesWithValue')),
            "metaTitle" => $this->metaTitle,
            "metaDescription" => $this->metaDescription,
            "media" => $this->whenLoaded('media', function () {
                return $this->media->isNotEmpty()
                    ? MediaResource::generate($this)
                    : [];
            }),

        ];


        return $response;


    }
}