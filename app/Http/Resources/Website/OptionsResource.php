<?php

namespace App\Http\Resources\Website;

use App\Http\Resources\ApiJsonResource;


class OptionsResource extends ApiJsonResource
{

    public function toArray($request)
    {




        return [
            'attributeOptionId' => $this['attributeOptionId'],
            'hexCode' => $this['option_hexCode'],
            'number' => $this['option_number'],
            'name' => $this['option_name'],
            'slug' => $this['option_slug'],
            'value' => $this['value'],

            // 'text' => $this['option_name'],
            // 'value' => $this['attributeOptionId'],
            // 'meta' => [
            //     'slug' => $this['slug'],
            // ],
        ];
    }
}
