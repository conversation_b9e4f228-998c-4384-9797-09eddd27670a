<?php

namespace App\Http\Resources\Website;

use App\Http\Resources\ApiJsonResource;
use App\Http\Resources\Website\OptionsResource;

class AttributeVarianceResource extends ApiJsonResource
{
    /**
     * @param $request
     * @return array
     */
    public function toArray($request): array
    {

        return [
            'attributeId' => $this->attributeId,
            'name' => $this->name,
            'key' => $this->key,
            'isRequired' => $this->isRequired,
            'slug' => $this->slug,
            'prefix' => $this->prefix,
            'suffix' => $this->suffix,
            'type' => $this->type,
            'hasFilter' => $this->hasFilter,
            'value' => ValueAttributeResource::generate($this),


        ];


        // return [
        //     'attributeId' => $this->attributeId,
        //     'name' => $this->name,
        //     'prefix' => $this->prefix,
        //     'suffix' => $this->suffix,
        //     'value' => ValueResource::generate($this->whenLoaded('varianceAttributesValue')),
        // ];

        // return $response;
    }
}