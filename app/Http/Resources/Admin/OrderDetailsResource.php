<?php

namespace App\Http\Resources\Admin;

use App\Http\Resources\ApiJsonResource;

class OrderDetailsResource extends ApiJsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array
     */
    public function toArray($request)
    {
        $response = parent::toArray($request);


        $response['total'] = $this->currencyPriceResource($this->total);
        $response["subTotal"] = $this->currencyPriceResource($this->subTotal);
        $response["tax"] = $this->currencyPriceResource($this->tax);
        $response["shippingPrice"] = $this->currencyPriceResource($this->shippingPrice);
        $response["orderItems"] = OrderItemResource::generate($this->whenLoaded('orderItems'));
        $response["address"] = AddressResource::generate($this->whenLoaded('address'));
        $response["paymentMethod"] = PaymentMethodResource::generate($this->whenLoaded('paymentMethod'));
        $response["shippingCarrier"] = PriceResource::generate($this->whenLoaded('shippingCarrier'));
        $response["user"] = UserResource::generate($this->whenLoaded('user'));


        return $response;
    }
}