<?php

namespace App\Http\Resources\General;

use App\Http\Resources\ApiJsonResource;

class MediaModelResource extends ApiJsonResource
{
    /**
     * @param $request
     * @return array
     */
    public function toArray($request): array
    {

        return [
            "id" => $this->id,
            "src" => $this->getFullUrl(),
            "preview" => $this->getFullUrl(),
            "fileSize" => $this->size,
            "mimeType" => $this->mime_type,
            "disk" => $this->disk,

        ];

    }
}