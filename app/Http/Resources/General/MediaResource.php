<?php

namespace App\Http\Resources\General;

use App\Http\Resources\ApiJsonResource;

class MediaResource extends ApiJsonResource
{
    /**
     * @param $request
     * @return array
     */
    private function setItem($item)
    {

        return [
            "id" => $item->id,
            "src" => $item->getFullUrl(),
            "preview" => $item->getFullUrl(),
            "fileSize" => $item['size'],
            "mimeType" => $item['mime_type'],
            "disk" => $item['disk'],
            'sort' => $item['sort'],
            "alt" => $item['alt'],

        ];




    }
    public function toArray($request): array
    {
        //! TODO: change _ to camelCase on response

        if (property_exists($this->getModel(), 'imagesCollection')) {
            $imagesCollection = [];
            if (is_array($this->imagesCollection)) {


                foreach ($this->imagesCollection as $key => $collection) {
                    $collectionMedia = $this->getMedia($collection)->sortBy('sort');
                    ;
                    if ($collectionMedia->count()) {
                        foreach ($collectionMedia as $media) {
                            $imagesCollection[$collection][] = $this->setItem($media);
                        }
                    } else {
                        $imagesCollection[$collection] = [];
                    }

                }
                return $imagesCollection;
            } else {
                $response = [];
                $media = $this->getFirstMedia($this->imagesCollection);
                if (empty($media)) {
                    return [$this->imagesCollection => null];
                }

                $response[$this->imagesCollection] = $this->setItem($media);
                return $response;

            }
        }

        return [];


    }
}