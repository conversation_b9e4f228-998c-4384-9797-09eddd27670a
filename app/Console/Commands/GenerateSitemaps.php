<?php

namespace App\Console\Commands;

use App\Enums\ProductTypeEnum;
use App\Models\Brand;
use App\Models\Category;
use App\Models\Page;
use App\Models\Product;
use Illuminate\Console\Command;
use App\Helper\XML\Sitemap;
use App\Helper\XML\Url;
use Spatie\Sitemap\SitemapIndex;
use Spatie\Sitemap\Tags\Sitemap as TagsSitemap;

class GenerateSitemaps extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'generate:sitemaps';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';


    /**
     * The console command description.
     *
     * @var string
     */
    protected $frontUrl = '';



    protected $disk = 'public';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {

        $this->frontUrl = env('FRONTEND_URL');

        $arabicSitemap = Sitemap::create();

        $arabicSitemap->add(
            Url::create("$this->frontUrl/ar")->setChangeFrequency('')
        );

        $englishSitemap = Sitemap::create();


        $englishSitemap->add(
            Url::create("$this->frontUrl/en")->setChangeFrequency('')// Use FRONTEND_URL for homepage
        );

        Page::all()->each(function (Page $page) use ($arabicSitemap, $englishSitemap) {
            $url = Url::create("$this->frontUrl/ar/page/$page->slug")->setChangeFrequency('');// Adjust the route as necessary
            $this->info($page->slug);
            $arabicSitemap->add($url);
            $url = Url::create("$this->frontUrl/en/page/$page->slug")->setChangeFrequency(''); // Adjust the route as necessary
            $this->info($page->slug);
            $englishSitemap->add($url);
        });

        $arabicSitemap->writeToDisk($this->disk, "sitemap/ar/static-sitemap.xml");

        $englishSitemap->writeToDisk($this->disk, "sitemap/en/static-sitemap.xml");



        $categorySitemapEnglish = Sitemap::create();
        $categorySitemapArabic = Sitemap::create();



        Category::all()->each(function (Category $category) use ($categorySitemapEnglish, $categorySitemapArabic) {

            $urlen = Url::create(url: "$this->frontUrl/en/category/" . $category->slug)->setChangeFrequency('');
            $urlar = Url::create("$this->frontUrl/ar/category/" . $category->slug)->setChangeFrequency('');

            if (!is_null($category->parentId)) {
                $urlen = Url::create(url: "$this->frontUrl/en/category/" . $category->parent->slug . "/" . $category->slug)->setChangeFrequency('');
                $urlar = Url::create("$this->frontUrl/ar/category/" . $category->parent->slug . "/" . $category->slug)->setChangeFrequency('');

                if (!is_null($category?->parent?->parent)) {
                    $urlen = Url::create(url: "$this->frontUrl/en/category/" . $category->parent->parent->slug . "/" . $category->parent->slug . "/" . $category->slug)->setChangeFrequency('');
                    $urlar = Url::create("$this->frontUrl/ar/category/" . $category->parent->parent->slug . "/" . $category->parent->slug . "/" . $category->slug)->setChangeFrequency('');
                }

                if (!is_null($category?->parent?->parent?->parent)) {
                    $urlen = Url::create(url: "$this->frontUrl/en/category/" . $category->parent->parent->parent->slug . "/" . $category->parent->parent->slug . "/" . $category->parent->slug . "/" . $category->slug)->setChangeFrequency('');
                    $urlar = Url::create("$this->frontUrl/ar/category/" . $category->parent->parent->parent->slug . "/" . $category->parent->parent->slug . "/" . $category->parent->slug . "/" . $category->slug)->setChangeFrequency('');
                }
            }

            $categorySitemapEnglish->add($urlen);
            $categorySitemapArabic->add($urlar);

        });


        $categorySitemapArabic->writeToDisk($this->disk, "sitemap/ar/listing-sitemap.xml");

        $categorySitemapEnglish->writeToDisk($this->disk, "sitemap/en/listing-sitemap.xml");





        $brandSitemapEnglish = Sitemap::create();
        $brandSitemapArabic = Sitemap::create();


        Brand::all()->each(function (Brand $brand) use ($brandSitemapEnglish, $brandSitemapArabic) {

            $url = Url::create(url: "$this->frontUrl/en/brands/" . $brand->slug)->setChangeFrequency('');

            $brandSitemapEnglish->add($url);

            $url = Url::create("$this->frontUrl/ar/brands/" . $brand->slug)->setChangeFrequency('');
            $brandSitemapArabic->add($url);

        });

        $brandSitemapArabic->writeToDisk($this->disk, "sitemap/ar/brands-sitemap.xml");

        $brandSitemapEnglish->writeToDisk($this->disk, "sitemap/en/brands-sitemap.xml");




        $productSitemapEnglish = Sitemap::create();
        $productSitemapArabic = Sitemap::create();


        Product::all()->each(function (Product $product) use ($productSitemapEnglish, $productSitemapArabic) {
            // Check if the product has a cover image

            if ($product->covers->count()) {

                $element = Url::create("$this->frontUrl/ar/product/" . $product->slug)

                    ->addImage($product->covers->first()->getFullUrl())->setChangeFrequency(''); // Ensure cover exists



                // if ($product->type == ProductTypeEnum::alternative->value && $product->variances->count()) {
                //     foreach ($product->variances as $variance) {
                //         $element->addAlternate("$this->frontUrl/ar/product/" . $product->slug . "/" . $variance->slug, 'ar')->setChangeFrequency('');
                //     }
                // }

                $productSitemapArabic->add($element);


                $element = Url::create("$this->frontUrl/en/product/" . $product->slug)
                    ->addImage($product->covers->first()->getFullUrl())->setChangeFrequency('');



                $productSitemapEnglish->add($element);

            }


            $this->info($product->slug);
            $this->info($product->slug);
        });



        $productSitemapArabic->writeToDisk($this->disk, "sitemap/ar/product-sitemap.xml");
        $productSitemapEnglish->writeToDisk($this->disk, "sitemap/en/product-sitemap.xml");


        $arabicBaseSitemap = SitemapIndex::create();
        $englishBaseSitemap = SitemapIndex::create();

        $arabicBaseSitemap
            ->add(TagsSitemap::create("$this->frontUrl/sitemap/ar/static-sitemap.xml"))
            ->add(TagsSitemap::create("$this->frontUrl/sitemap/ar/listing-sitemap.xml"))
            ->add(TagsSitemap::create("$this->frontUrl/sitemap/ar/brands-sitemap.xml"))
            ->add(TagsSitemap::create("$this->frontUrl/sitemap/ar/product-sitemap.xml"));

        $arabicBaseSitemap->writeToDisk($this->disk, "sitemap/ar/sitemap.xml");

        $englishBaseSitemap
            ->add(TagsSitemap::create("$this->frontUrl/sitemap/en/static-sitemap.xml"))
            ->add(TagsSitemap::create("$this->frontUrl/sitemap/en/listing-sitemap.xml"))
            ->add(TagsSitemap::create("$this->frontUrl/sitemap/en/brands-sitemap.xml"))
            ->add(TagsSitemap::create("$this->frontUrl/sitemap/en/product-sitemap.xml"));


        $englishBaseSitemap->writeToDisk($this->disk, "sitemap/en/sitemap.xml");

    }
}