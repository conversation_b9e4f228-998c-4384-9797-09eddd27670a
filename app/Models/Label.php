<?php

/**
 * Created by Reliese Model.
 */

namespace App\Models;

use App\Traits\HasTranslations;
use App\Traits\Models\HasFilters;
use Carbon\Carbon;
use App\Traits\Models\Lookable;
use App\Traits\Models\Searchable;



/**
 * Class Label
 *
 * @property int $labelId
 * @property string $name
 * @property string $slug
 * @property string $color
 * @property Carbon $createdAt
 * @property Carbon $updatedAt

 *
 * @package App\Models
 */
class Label extends BaseModel
{
    use Lookable, Searchable, HasTranslations;
    const CREATED_AT = 'createdAt';
    const UPDATED_AT = 'updatedAt';
    protected $table = 'labels';
    protected $primaryKey = 'labelId';
    protected $perPage = 24;
    public static $snakeAttributes = false;

    protected $casts = [
        'labelId' => 'int',
        'color' => 'string',
        'slug' => 'string',
        'name' => 'json',
    ];

    public $translatable = [
        'name'
    ];
    protected $fillable = [
        'name',
        'color',
        'slug',
    ];


    public function allowedSearchAttributes(): array
    {
        return [
            'name->ar',
            'name->en',

        ];
    }


    public function getLookupResourceConfig(): array
    {
        return [
            'text_column' => 'name',
            'value_column' => 'labelId',
            'meta' => [
                'slug',
            ]
        ];
    }



}