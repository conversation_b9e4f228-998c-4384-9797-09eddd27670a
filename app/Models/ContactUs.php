<?php

namespace App\Models;

use App\Traits\Models\Searchable;
use App\Traits\HasTranslations;
use Carbon\Carbon;
use App\Traits\Models\HasFilters;
use Illuminate\Database\Eloquent\Builder;


/**
 * Class Color
 *
 * @property int $contactUsId
 * @property string $name
 * @property string $email
 * @property text $message
 * @property string $problemType
 * @property Carbon $createdAt
 * @property Carbon $updatedAt
 *
 *
 * @package App\Models
 */

class ContactUs extends BaseModel
{

    use HasTranslations, Searchable, HasFilters;
    const CREATED_AT = 'createdAt';
    const UPDATED_AT = 'updatedAt';
    protected $table = 'contact_us';
    protected $primaryKey = 'contactUsId';
    protected $perPage = 24;
    public static $snakeAttributes = false;
    public $translatable = ['name'];


    protected $casts = [
        'phone' => 'json',

    ];

    protected $fillable = [
        'name',
        'phone',
        'email' .
        'message',
        'problemType',
        'status'
    ];

    public function allowedFilters(): array
    {
        return [];

    }



    public function allowedSearchAttributes(): array
    {
        return [
            'name->ar',
            'name->en',
            'phone',
            'email',
            'message',
            'problemType',
            'status',
            'createdAt',
            'updatedAt',

        ];
    }


}