<?php

/**
 * Created by Reliese Model.
 */

namespace App\Models;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use App\Traits\Models\Lookable;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;

/**
 * Class Address
 *
 * @property int $addressId
 * @property int $cityId
 * @property string $address
 * @property string $district
 * @property string $phone	
 * @property string $recipientName
 * @property string $buildingNumber
 * @property string $default
 * 
 * 
 * 
 * @property Carbon $createdAt
 * @property Carbon $updatedAt
 *
 * @property Collection|Shipping[] $shippings
 * @property Collection|Supplier[] $suppliers
 *
 * @package App\Models
 */
class Address extends BaseModel
{
	use HasFactory, Lookable;
	const CREATED_AT = 'createdAt';
	const UPDATED_AT = 'updatedAt';
	protected $table = 'addresses';
	protected $primaryKey = 'addressId';
	protected $perPage = 24;
	public static $snakeAttributes = false;
	protected $casts = [
		'cityId' => 'int',
		'phone' => 'json'
	];

	protected $fillable = [
		'cityId',
		'district',
		'phone',
		'userId',
		'visitorId',
		'recipientName',
		'apartmentNumber',
		'buildingNumber',
		'firstName',
		'lastName',
		'email',
		'default',
	];

	public function user()
	{
		return $this->belongsTo(User::class, 'userId');
	}


	public function city()
	{
		return $this->belongsTo(City::class, 'cityId');
	}

	// public function shippings(): BelongsToMany
	// {
	// 	return $this->belongsToMany(Shipping::class, 'shippings_addresses', 'addressId', 'shippingId')
	// 		->withPivot('shippingAddressId')
	// 		->withTimestamps();
	// }

	public function suppliers(): BelongsToMany
	{
		return $this->belongsToMany(Supplier::class, 'suppliers_addresses', 'addressId', 'supplierId')
			->withPivot('supplierAddressId')
			->withTimestamps();
	}

	public function getLookupResourceConfig(): array
	{
		return [
			'text_column' => 'district',
			'value_column' => 'addressId',
		];
	}





}