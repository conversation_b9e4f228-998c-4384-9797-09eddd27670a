<?php

namespace App\Models\Filters;
use Closure;
use Carbon\Carbon;

use App\Repositories\Rating\RatingRepositoryInterface;
use App\Filters\Interfaces\WithOptions;
use App\Http\Resources\OptionsResource;
use Illuminate\Database\Eloquent\Builder;
use App\Models\Filters\Interfaces\FilterInterface;

class StatusFilter implements FilterInterface
{
    public function handle(Builder $query, Closure $next): mixed
    {
        $status = $this->getValue();
        return is_null($status) ? $next($query) : $next($query)->where('status', $status);

    }

    public function getValue(): mixed
    {
        return request()->has('status') ? request()->string('status') : null;

    }


}