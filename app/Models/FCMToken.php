<?php

namespace App\Models;

use Carbon\Carbon;

use Illuminate\Database\Eloquent\Relations\HasOne;

/**
 * Class Currency
 *
 * @property int $fcmTokenId
 * @property int $userId
 * @property string $device
 * @property Carbon $expiryDate
 * @property string $token
 * @property Carbon $createdAt
 * @property Carbon $updatedAt
 *
 * @property Price|null $price
 *
 * @package App\Models
 */

class FCMToken extends BaseModel
{
 
    const CREATED_AT = 'createdAt';
    const UPDATED_AT = 'updatedAt';
    protected $table = 'fcm_token';
    protected $primaryKey = 'fcmTokenId';
    protected $perPage = 24;
  
    protected $fillable = [
        'token',
        'userId',
        'device',
        'expiryDate'
    ];

    protected $dates = [
		'expiryDate',
	];

    public function user():HasOne
    {
        return $this->hasOne(User::class, 'userId');
    }




}