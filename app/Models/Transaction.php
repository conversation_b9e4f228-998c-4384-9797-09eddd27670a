<?php

/**
 * Created by Reliese Model.
 */

namespace App\Models;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

/**
 * Class Transaction
 * 
 * @property int $transactionId
 * @property float $openingBalance
 * @property float $amount
 * @property int $closingBalance
 * @property int $userId
 * @property string $status
 * @property int $paymentMethodId
 * @property array $response
 * @property array $description
 * @property int $relatedTransactionId
 * @property Carbon $createdAt
 * @property Carbon $updatedAt
 * 
 * @property PaymentsMethod $paymentsMethod
 * @property Transaction $transaction
 * @property User $user
 * @property Collection|Bid[] $bids
 * @property Collection|Transaction[] $transactions
 *
 * @package App\Models
 */
class Transaction extends BaseModel
{
	const CREATED_AT = 'createdAt';
	const UPDATED_AT = 'updatedAt';

	const DELETED_AT = 'deletedAt';

	protected $table = 'transactions';
	protected $primaryKey = 'transactionId';
	protected $perPage = 24;
	public static $snakeAttributes = false;

	protected $casts = [
		'openingBalance' => 'float',
		'amount' => 'float',
		'closingBalance' => 'int',
		'userId' => 'int',
		'visitorId' => 'int',
		'paymentMethodId' => 'int',
		'response' => 'json',
		'description' => 'json',
		'relatedTransactionId' => 'int',
		'model_id' => 'int',
		'model_type' => 'string',
		'checkoutId' => 'string'

	];

	protected $fillable = [
		'openingBalance',
		'amount',
		'closingBalance',
		'model_type',
		'model_id',
		'userId',
		'visitorId',
		'status',
		'paymentMethodId',
		'response',
		'description',
		'relatedTransactionId'
	];

	public function paymentsMethod(): BelongsTo
	{
		return $this->belongsTo(PaymentsMethod::class, 'paymentMethodId');
	}

	public function transaction(): BelongsTo
	{
		return $this->belongsTo(Transaction::class, 'relatedTransactionId');
	}

	public function user(): BelongsTo
	{
		return $this->belongsTo(User::class, 'userId');
	}

	public function bids(): HasMany
	{
		return $this->hasMany(Bid::class, 'transactionId');
	}

	public function transactions(): HasMany
	{
		return $this->hasMany(Transaction::class, 'relatedTransactionId');
	}
}