<?php

namespace App\Repositories\Rating;

use App\Repositories\BaseRepositoryInterface;

/**
 * @mixin BaseRepositoryInterface
 */
interface RatingRepositoryInterface
{
  /**
   * Get all  Ratings
   * @return  Ratings
   */
  public function getAllRatings();
  /**
   * @param int|string $id
   */
  public function deleteRating(int|string $id);


  /**
   * Get User Ratings
   * @return Ratings
   */
  public function getUserRatings();

  /**
   * unpublished Rating by ID .
   * @param int|string $id
   */
  public function getRatingsProductById($id);


}