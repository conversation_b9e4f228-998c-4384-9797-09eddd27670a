<?php

namespace App\Repositories\Rating;

use App\Models\Rating;
use App\Repositories\BaseRepository;
use App\Repositories\Rating\RatingRepositoryInterface;


class RatingRepository extends BaseRepository implements RatingRepositoryInterface
{

    protected $modelClass = Rating::class;

    public function getAllRatings()
    {
        return $this->getAllLatestPaginatedFiltered(with: ['user']);

    }

    public function deleteRating(int|string $id): mixed
    {
        return $this->destroy($id);
    }



    public function publishingRating(int|string $id)
    {
        $rating = $this->query()
            ->findOrFail($id);
        if ($rating->status == 'Published') {
            $rating->update(['status' => 'unPublished']);
        } else {
            $rating->update(['status' => 'Published']);
        }


        return $rating;
    }


    public function getUserRatings()
    {
        $userId = auth(GUARD_API)->user()->userId;
        return $this->query()->with('product:productId,description,name')->where('userId', $userId)->customPaginate();
    }


    public function getRatingsProductById($id)
    {

        return $this->query()
            ->where('productId', $id)
            ->where('status', PUBLISHED)
            ->orderBy('rating', 'desc')
            ->with(['user', 'user.media'])
            ->get();

    }

}