<?php

namespace App\Repositories\Product;

use Illuminate\Database\Eloquent\Model;
use App\Repositories\BaseRepositoryInterface;

/**
 * @mixin BaseRepositoryInterface
 */
interface ProductRepositoryInterface
{
    /**
     * Store  Product .
     *
     * @param array $data
     * @return Model
     */
    public function storeProduct(array $data): Model;


    /**
     * Store  Product .
     *
     * @param array $data
     * @return Model
     */
    public function storeDraftProduct(array $data): Model;

    /**
     * Update Product
     *
     * @param int|string $id
     * @param array $data
     * @return Model
     */
    public function updateProduct(int|string $id, array $data): Model;
    /**
     * Get all  Products
     * @return  Products
     */
    public function getAllProducts();
    /**
     * @param int|string $id
     * @return mixed
     */
    public function findByIdProduct(int|string $id): mixed;
    /**
     * @param int|string $id
     */
    public function deleteProduct(int|string $id);
    /**
     * Store Product Attributes.
     * @param object $product
     * @param  array $data
     */
    public function storeProductAttributes(object $product, int|array $data): mixed;
    /**
     * get Product Attributes
     * @param object $product
     * @return Mixed
     */
    public function getProductAttributes(object $product);
    /**
     * Store Product Variance.
     * @param object $product
     * @param  array $data
     */
    public function storeProductVariance(object $product, int|array $attributeId): mixed;
    /**
     * Store Product Media.
     * @param int|array $mediaId
     * @param object $product
     * @return Mixed
     */

    public function getCategoryAttributes(int $category);
    /**
     * get Attribute Values
     * @param int $attributeId
     * @return Mixed
     */
    // public function getAttributeOptions(int $attribute): mixed;
    public function getAttributesWithParents();

    /**
     * Store Rating On Product .
     * @param int|string $productId
     * @param array $data
     * @return Model
     */
    public function storeRatingOnProduct(int|string $product, array $data);
    /**
     * Get getRatingsProduct
     */
    public function getRatings(int|string $product);

    /**
     * get Product With Variance
     *  @param int|string $productId
     *  @param int|string $variance
     */
    public function getProductWithVariance(int|string $product, int|string $variance = null);

    /**
     * get Product With Variance
     *  @param int|string $productId
     *  @param int|string $variance
     */
    public function getProductDetails(int|string $product, int|string $variance = null);

    /**
     * Get getRecentlyAddedProducts
     * @return Products
     */
    public function getRecentlyAddedProducts();

    /**
     * @param string $category
     * @param string $subCategory
     * @return Model
     */
    public function getProductsByCategory(int|string $category, int|string $subcategory = null): mixed;

    //  public function productsColorsMedia(int|array $mediaId, object $product): mixed;



    /**
     * @param array $ids
     */
    public function getProductByIds(array $ids);

    /**
     * get Attribute Values
     * @param array $data
     * @return Mixed
     */
    // public function getAttributeOptions(int $attribute): mixed;
    public function getProductVarianceDetails($data);


    /**
     * get Attribute Values
     * @param string $oldSlug
     * @return Mixed
     */
    public function getProductDetailsByOldSlug($oldSlug);



    /**
     * get Attribute Values
     * @param string $oldId
     * @return Mixed
     */
    public function getProductDetailsByOldId($oldId);


    /**
     * 
     * @return Mixed
     */
    public function mostPopular();

    /**
     * 
     * @return Mixed
     */
    public function newArrival();



    public function suggestedProduct($id);

}
