<?php


namespace App\Repositories\Address;

use App\Models\Address;
use App\Models\User;
use App\Models\Visitor;
use App\Repositories\BaseRepository;
use App\Repositories\Address\AddressRepositoryInterface;
use Illuminate\Database\Eloquent\Model;

class AddressRepository extends BaseRepository implements AddressRepositoryInterface
{

    public $modelClass = Address::class;

    public function storeAddress(array $data): Model
    {


        $user = auth(GUARD_API)->user();

        $default = false;
        if (isVisitor()) {
            $default = $this->query()->where('visitorId', $user->visitorId) === 0 ? true : false;
        } else {
            $default = $this->query()->where('userId', $user->userId) === 0 ? true : false;
        }

        $recipientName = explode(' ', $data['recipientName']);
        $firstName = $recipientName[0];
        $lastName = count(value: $recipientName) > 1 && $recipientName[1] != "" ? $recipientName[1] : $recipientName[0];

        $address = $this->store([
            'userId' => !isVisitor() ? $user->userId : null,
            'visitorId' => isVisitor() ? $user->visitorId : null,
            'cityId' => $data['cityId'],
            'district' => $data['district'],
            'phone' => $data['phone'],
            'street' => $data['street'] ?? null,
            'recipientName' => $data['recipientName'] ?? null,
            'apartmentNumber' => $data['apartmentNumber'] ?? null,
            'buildingNumber' => $data['buildingNumber'] ?? null,
            'default' => $default,
            'firstName' => $firstName,
            'lastName' => $lastName
        ]);


        return $address;
    }

    public function updateAddress(int|string $id, array $data): Model
    {

        $user = auth(GUARD_API)->user();


        $recipientName = explode(' ', $data['recipientName']);
        $firstName = $recipientName[0];
        $lastName = count(value: $recipientName) > 1 && $recipientName[1] != "" ? $recipientName[1] : $recipientName[0];

        $address = $this->update($id, [
            'userId' => !isVisitor() ? $user->userId : null,
            'visitorId' => isVisitor() ? $user->visitorId : null,
            'cityId' => $data['cityId'],
            'district' => $data['district'],
            'phone' => $data['phone'],
            'street' => $data['street'] ?? null,
            'recipientName' => $data['recipientName'],
            'apartmentNumber' => $data['apartmentNumber'] ?? null,
            'buildingNumber' => $data['buildingNumber'] ?? null,
            'default' => $data['default'] ?? false,
            'firstName' => $firstName,
            'lastName' => $lastName
        ]);

        return $address;
    }

    public function setDefaultAddress(int|string $id)
    {



        $query = $this->query();
        if (isVisitor()) {
            $query->where('visitorId', auth(GUARD_API)->user()->visitorId);
        } else {
            $query->where('userId', auth(GUARD_API)->user()->userId);
        }

        $query->update(['default' => '0']);
        ;


        $this->update($id, ['default' => '1']);

        return $this->findById($id);
    }

    public function getAllAddresses()
    {
        return $this->getAllPaginated();
    }

    public function getUserAddresses()
    {

        $user = auth(GUARD_API)->user();
        $query = $this->query()->with(['city', 'city.country']);
        if (isVisitor()) {
            return $query->where('visitorId', $user->visitorId)->get();
        }
        return $query->where('userId', $user->userId)->get();

    }


    public function findByIdAddress(int|string $id): mixed
    {
        return $this->query()
            ->with(['city', 'city.country'])
            ->findOrFail($id);
    }

    public function deleteAddress(int|string $id): mixed
    {
        return $this->destroy($id);
    }
}