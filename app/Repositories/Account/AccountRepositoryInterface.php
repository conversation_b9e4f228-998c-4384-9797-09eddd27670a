<?php

namespace App\Repositories\Account;

use App\Repositories\BaseRepositoryInterface;

/**
 * @mixin BaseRepositoryInterface
 */
interface AccountRepositoryInterface
{
    public function getUserInformation();

    public function updateUserImage(array $data);

    public function updateUserInformation(array $data);

    public function updateUserName(array $data);


    public function updatePassword(array $data);

    public function requestChange(array $data);

    public function getUserGeneralInformation();


    public function getTransactions();

    public function updateUserEmail(array $data);

    public function updateUserPhoneNumber(array $data);





}