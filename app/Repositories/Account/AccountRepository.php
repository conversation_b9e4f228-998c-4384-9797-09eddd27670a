<?php

namespace App\Repositories\Account;

use App\Enums\MethodOTPEnum;
use App\Enums\RequestChangeNameEnum;
use App\Enums\SMSProvidersEnum;
use App\Enums\UserStatusEnum;
use App\Models\OtpNotification;
use App\Repositories\BaseRepository;
use App\Models\User;
use App\Models\Transaction;
use App\Models\RequestChangeName;
use App\SMSProviders\SMSProvidersFactory;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\Hash;
use App\Repositories\Account\AccountRepositoryInterface;

class AccountRepository extends BaseRepository implements AccountRepositoryInterface
{
    protected $modelClass = User::class;

    public function getUserGeneralInformation()
    {

        $user = $this->findById(auth(GUARD_API)->user()->userId, ['*'], ['media', 'roles']);
        return $user;

    }

    public function getUserInformation()
    {
        $user = $this->findById(auth(GUARD_API)->user()->userId, ['*'], ['media', 'roles']);
        return $user;
    }


    public function updateUserName(array $data)
    {
        $user = $this->query()->findOrFail(auth(GUARD_API)->user()->userId);

        $requestName = RequestChangeName::create([
            'firstName' => $data['firstName'] ?? $user->firstName,
            'lastName' => $data['lastName'] ?? $user->lastName,
            'status' => RequestChangeNameEnum::pending->value,
            'userId' => auth(GUARD_API)->user()->userId,
            'previousFirstName' => $user->firstName,
            'previousLastName' => $user->lastName,
        ]);

        $data['firstName'] = $user->firstName;
        $data['lastName'] = $user->lastName;

        return $user;


    }


    public function updateUserPhoneNumber(array $data)
    {
        $user = $this->query()->findOrFail(auth(GUARD_API)->user()->userId);

        $smsProvidersFactory = SMSProvidersFactory::createSMSGateway(SMSProvidersEnum::arabiacell->value);
        $otp = rand(100000, 999999);

        $otpModel = OtpNotification::create([
            'otp' => $otp,
            'userId' => auth(GUARD_API)->user()->userId,
            'expiryDate' => Carbon::now()->addDay(),
            'method' => MethodOTPEnum::phone->value,
        ]);

        $phone = $data['phone']['code'] . $data['phone']['number'];


        $smsProvidersFactory->send($phone, $otp);

        return $user;


    }


    public function updateUserEmail(array $data)
    {
        $user = $this->query()->findOrFail(auth(GUARD_API)->user()->userId);

        $user = $this->update(
            auth(GUARD_API)->user()->userId,
            [
                'email' => $data['email'],
            ]

        );
        return $user;


    }

    public function updateUserInformation(array $data)
    {


        $user = $this->query()->findOrFail(auth(GUARD_API)->user()->userId);

        if (isset($data['media']['avatar']['src']) && !is_null($data['media']['avatar'])) {
            $user->addMediaToModel($data['media']['avatar'], 'avatar');
            unset($data['media']);
        }

        $user = $this->update(
            auth(GUARD_API)->user()->userId,
            [
                'birthday' => $data['birthday'],
                'gender' => $data['gender'],
            ]

        );

        return $user;

    }

    public function updateUserImage(array $data)
    {
        $user = $this->findById(auth(GUARD_API)->user()->userId);

        if (isset($data['media']['src'])) {
            $user->clearMediaByCollection($data['media'], 'avatar', $user->userId);
            $user->addMediaToModel($data['media'], 'avatar');
        }

        return $user;

    }

    public function updatePassword(array $data)
    {
        $userId = auth(GUARD_API)->user()->userId;
        $user = $this->query()->findOrFail($userId);
        $user->update(['password' => Hash::make($data['password'])]);
        return ['passwordChanged' => true];
    }

    public function requestChange(array $data)
    {
        $userId = auth(GUARD_API)->user()->userId;
        $user = $this->query()->findOrFail($userId);

        $lastName = isset($data['lastName']) ? $data['lastName'] : $user->lastName;
        $firstName = isset($data['firstName']) ? $data['firstName'] : $user->firstName;

        $requestName = RequestChangeName::create([
            'firstName' => $data['firstName'] ?? $user->firstName,
            'lastName' => $data['lastName'] ?? $user->lastName,
            'status' => RequestChangeNameEnum::pending->value,
            'userId' => auth(GUARD_API)->user()->userId,
            'previousFirstName' => $user->firstName,
            'previousLastName' => $user->lastName,
        ]);

        return $requestName;

    }

    public function getTransactions()
    {
        $user = auth(GUARD_API)->user()->userId;
        $transactions = Transaction::where('userId', $user)->customPaginate();
        return $transactions;

    }



}