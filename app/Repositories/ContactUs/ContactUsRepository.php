<?php

namespace App\Repositories\ContactUs;

use App\Models\ContactUs;
use App\Repositories\BaseRepository;
use App\Repositories\ContactUs\ContactUsRepositoryInterface;
use Illuminate\Database\Eloquent\Model;


class ContactUsRepository extends BaseRepository implements ContactUsRepositoryInterface
{

    protected $modelClass = ContactUs::class;

    public function storeMessage(array $data): Model
    {
        $message = $this->store([
            'name' => $data['name'],
            'phone' => $data['phone'],
            'email' => $data['email'],
            'problemType' => $data['problemType'],
            'message' => $data['message'],

        ]);

        return $message;
    }


    public function getAllMessages()
    {
        return $this->getAllLatestPaginatedFiltered();
    }

    public function findByIdContactUs(int|string $id)
    {
        return $this->query()
            ->findOrFail($id);

    }

    public function updateStatus(int|string $id, array $data): Model
    {
        $contact = $this->update($id, [
            'status' => $data['status']

        ]);

        return $contact;
    }


    public function deleteMessage(int|string $id): mixed
    {
        return $this->destroy($id);
    }
}