<?php

namespace App\Repositories\FCMToken;

use App\Models\FCMToken;
use App\Repositories\BaseRepository;
use Illuminate\Database\Eloquent\Model;
use App\Repositories\FCMToken\FCMTokenRepositoryInterface;


class FCMTokenRepository extends BaseRepository implements FCMTokenRepositoryInterface
{

    public $modelClass = FCMToken::class;

    public function store(array $data): Model
    {

        $fcmToken = parent::store([
            'token' => $data['token'],
            'userId' => auth(GUARD_API)->user()->userId,
            'device' => $data['device'],
            'expiryDate' => $data['expiryDate'],
        ]);



        return $fcmToken;

    }

    public function updateToken(int|string $id, array $data): Model
    {

        $fcmToken = $this->update($id, [
            'token' => $data['token'],
            'userId' => auth(GUARD_API)->user()->userId,
            'device' => $data['device'],
            'expiryDate' => $data['expiryDate'],
        ]);

        return $fcmToken;
    }



    public function get()
    {
        return $this->query()->where('userId', auth(GUARD_API)->user()->userId)->get();
    }




    public function findTokenById(int|string $id): mixed
    {
        return $this->query()
            ->findOrFail($id);
    }



    public function delete(int|string $id): mixed
    {
        return $this->destroy($id);
    }



}
