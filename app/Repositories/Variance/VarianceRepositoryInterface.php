<?php

namespace App\Repositories\Variance;

use Illuminate\Database\Eloquent\Model;
use App\Repositories\BaseRepositoryInterface;

/**
 * @mixin BaseRepositoryInterface
 */
interface VarianceRepositoryInterface
{
    /**
     * Store Variance .
     *
     * @param array $data
     * @return Model
     */
    public function storeVariance(array $data): Model;

    /**
     * Update Variance
     *
     * @param int|string $id
     * @param array $data
     * @return Model
     */
    public function updateVariance(int|string $id, array $data): Model;

    /**
     * Get all Variances
     * @return Variances
     */
    public function getAllVariances();
    /**
     * @param int|string $id
     * @return mixed
     */
    public function findByIdVariance(int|string $id): mixed;

    /**
     * @param int|string $id
     */
    public function deleteVariance(int|string $id);
}
