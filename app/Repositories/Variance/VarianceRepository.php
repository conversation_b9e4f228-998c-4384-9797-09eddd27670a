<?php

namespace App\Repositories\Variance;

use App\Models\Variance;
use App\Repositories\BaseRepository;
use App\Repositories\Variance\VarianceRepositoryInterface;
use Illuminate\Database\Eloquent\Model;

class VarianceRepository extends BaseRepository implements VarianceRepositoryInterface
{

    protected $modelClass = Variance::class;

    public function storeVariance(array $data): Model
    {

        $metaTitle = isset($data['metaTitle']) ? $data['metaTitle'] : null;
        $metaDescription = isset($data['metaDescription']) ? $data['metaDescription'] : null;

        $variance = $this->store([
            'brandId' => $data['brandId'],
            'attributeId' => $data['attributeId'],
            'SKU' => $data['SKU'],
            'name' => $data['name'],
            'slug' => $data['name'],
            'type' => $data['type'],
            'metaTitle' => $metaTitle,
            'metaDescription' => $metaDescription,
        ]);

        return $variance;
    }

    public function updateVariance(int|string $id, array $data): Model
    {
        $metaTitle = isset($data['metaTitle']) ? $data['metaTitle'] : null;
        $metaDescription = isset($data['metaDescription']) ? $data['metaDescription'] : null;

        $variance = $this->update($id, [
            'brandId' => $data['brandId'],
            'attributeId' => $data['attributeId'],
            'SKU' => $data['SKU'],
            'name' => $data['name'],
            'slug' => $data['name'],
            'type' => $data['type'],
            'metaTitle' => $metaTitle,
            'metaDescription' => $metaDescription
        ]);

        return $variance;
    }

    public function getAllVariances()
    {
        return $this->getAllPaginated();
    }

    public function findByIdVariance(int|string $id): mixed
    {
        return $this->query()
            ->with(
                [
                    'alternatives',
                    'attributesValues',
                    'bundles',
                    'carts',
                    'orderItems',
                    'attributes',
                    'colors',
                    'stocks',

                ]
            )->findOrFail($id);
    }

    public function deleteVariance(int|string $id): mixed
    {
        return $this->destroy($id);
    }


    public function getVariancesIds(array $ids): mixed
    {
        return $this->query()->whereIn('varianceId', $ids)->with([
            'activeStocks',
            'activeStocks.price',
            'product',
            'product.brand',
            'product.categories',
            'media',
        ])->customPaginate();
    }
}
