<?php

namespace App\Repositories\Cart;

use App\Enums\ProductTypeEnum;
use App\Helper\Calculator;
use App\Models\Cart;
use App\Models\Product;
use App\Models\User;
use App\Models\Visitor;
use App\Repositories\BaseRepository;
use App\Repositories\Cart\CartRepositoryInterface;
use App\Repositories\Product\ProductRepositoryInterface;
use Illuminate\Http\Response;

class CartRepository extends BaseRepository implements CartRepositoryInterface
{

    public $modelClass = Cart::class;


    private function _get()
    {
        $with = [
            'product',
            'product.media',
            'variance',
            'variance.activeStock',
            'variance.media',
            'product.activeStock',
            // 'bundle',
            // 'bundle.activeStock',
            // 'bundle.media',
        ];


        $query = $this->query()->with($with);

        if (isVisitor()) {
            $query->where('visitorId', auth(GUARD_API)->user()->visitorId);
        } else {
            $query->where('userId', auth(GUARD_API)->user()->userId);
        }

        $items = $query->orderBy('createdAt', 'desc')
            ->get();


        foreach ($items as $key => $item) {

            if ($item->quantity > $item->variance?->activeStock?->quantity ?? 0) {
                $item = apply_filters('cart.no-quantity-item', $item);
            }

        }

        $items = apply_filters('cart.after-prepare-cart-items', $items);

        $total = Calculator::totalFromCart($items);

        return [
            'items' => $items,
            'total' => $total
        ];


    }


    public function getCart()
    {

        return $this->_get();

    }


    public function storeProductToCart(array $data)
    {
        $bundleId = isset($data['bundleId']) ? $data['bundleId'] : null;
        $varianceId = isset($data['varianceId']) ? $data['varianceId'] : null;

        $user = auth(GUARD_API)->user();
        $modelName = $user->getMorphClass();

        $this->updateOrCreate([
            'userId' => $modelName === User::class ? auth(GUARD_API)->user()->userId : null,
            'visitorId' => $modelName === Visitor::class ? auth(GUARD_API)->user()->visitorId : null,
            'productId' => $data['productId'],
            'varianceId' => $varianceId,
        ], [
            'quantity' => $data['quantity'],
            'bundleId' => $bundleId,
            'varianceId' => $varianceId,
        ]);

        return $this->_get();

    }

    public function updateCart(array $data)
    {


        $bundleId = isset($data['bundleId']) ? $data['bundleId'] : null;
        $varianceId = isset($data['varianceId']) ? $data['varianceId'] : null;

        $user = auth(GUARD_API)->user();
        $modelName = $user->getMorphClass();
        $this->updateOrCreate([
            'userId' => $modelName === User::class ? auth(GUARD_API)->user()->userId : null,
            'visitorId' => $modelName === Visitor::class ? auth(GUARD_API)->user()->visitorId : null,
            'productId' => $data['productId'],
            'varianceId' => $varianceId,
        ], [
            'quantity' => $data['quantity'],
            'bundleId' => $bundleId,
            'varianceId' => $varianceId,
        ]);




        return $this->_get();


    }
    public function deleteCart(int|string $cartId)
    {
        $this->destroy($cartId);
        return $this->_get();

    }


    public function removeUserCart()
    {

        $query = $this->query();

        if (isVisitor()) {
            $query->where('visitorId', auth(GUARD_API)->user()->visitorId);
        } else {
            $query->where('userId', auth(GUARD_API)->user()->userId);
        }

        $items = $query->delete();

        return $items;
    }

}