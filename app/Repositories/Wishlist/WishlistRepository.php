<?php

namespace App\Repositories\Wishlist;

use App\Models\Wishlist;
use App\Repositories\BaseRepository;
use App\Models\Product;
use Auth;
use App\Repositories\Wishlist\WishlistRepositoryInterface;
use App\Repositories\Product\ProductRepositoryInterface;


class WishlistRepository extends BaseRepository implements WishlistRepositoryInterface
{

    public $modelClass = Wishlist::class;


    public function __construct(protected ProductRepositoryInterface $productRepository)
    {

    }

    public function getUserWishlist()
    {
        $query = $this->query()->with([
            'product',
            'product.media',
            'product.brand',
            'product.variance',
            'product.variance.attributes',
            'product.variance.attributes.options',
            'product.variance.activeStock',
            'product.variance.media',
            'product.categories',
        ]);

        if (isVisitor()) {
            $query->where('visitorId', auth(GUARD_API)->user()->visitorId);
        } else {
            $query->where('userId', auth(GUARD_API)->user()->userId);
        }

        return $query->get();

    }

    public function removeFromWishlist($productId)
    {


        $product = resolve(ProductRepositoryInterface::class)->findById($productId);

        $query = $this->query()->where('productId', $productId);

        if (isVisitor()) {
            $query->where('visitorId', auth(GUARD_API)->user()->visitorId);
        } else {
            $query->where('userId', auth(GUARD_API)->user()->userId);
        }
        $query->delete();

        return $product;

    }

    public function addToWishlist($data)
    {

        $wishlist = $this->store([
            'userId' => !isVisitor() ? auth(GUARD_API)->user()->userId : null,
            'visitorId' => isVisitor() ? auth(GUARD_API)->user()->visitorId : null,
            'productId' => $data['productId'],
        ]);

        return $wishlist->load('product');
        ;

    }

}